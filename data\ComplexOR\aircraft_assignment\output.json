["The problem is solved.", {"background": "The Aircraft Assignment Problem aims to assign aircraft to routes in order to minimize the total cost while satisfying demand constraints with available aircraft. The problem involves a set of aircraft and a set of routes. Given the costs of assigning an aircraft to a route, the objective is to minimize the total cost of the assignment. There are limited available aircraft. It is constrained that the number of each aircraft allocated does not exceed its available number. Given the demand of each route and the capabilities (the largest number of people can be carried) of an aircraft for a route, the demand constraint ensures that the total allocation for each route satisfies the demand.", "problem_type": "LP", "parameters": [{"symbol": "AircraftNum", "definition": "The number of aircraft available", "shape": [], "code": "AircraftNum = data[\"AircraftNum\"] # scalar parameter"}, {"symbol": "RouteNum", "definition": "The number of routes to be served", "shape": [], "code": "RouteNum = data[\"RouteNum\"] # scalar parameter"}, {"symbol": "availability", "definition": "The number of each aircraft available for assignment", "shape": ["AircraftNum"], "code": "availability = np.array(data[\"availability\"]) # ['AircraftNum']"}, {"symbol": "demand", "definition": "The passenger demand for each route", "shape": ["RouteNum"], "code": "demand = np.array(data[\"demand\"]) # ['RouteNum']"}, {"symbol": "capabilities", "definition": "The passenger capacity of each aircraft for each route", "shape": ["AircraftNum", "RouteNum"], "code": "capabilities = np.array(data[\"capabilities\"]) # ['AircraftNum', 'RouteNum']"}, {"symbol": "costs", "definition": "The cost of assigning each aircraft to each route", "shape": ["AircraftNum", "RouteNum"], "code": "costs = np.array(data[\"costs\"]) # ['AircraftNum', 'RouteNum']"}], "constraint": [{"description": "The number of each aircraft allocated must not exceed its availability", "status": "coded", "formulation": "\\sum_{j=1}^{\\textup{RouteNum}} \\textup{Assignment}_{i,j} \\leq \\textup{availability}_{i} \\quad \\forall i \\in \\{1, 2, \\ldots, \\textup{AircraftNum}\\}", "related_variables": ["Assignment"], "related_parameters": ["RouteNum", "availability", "AircraftNum"], "code": "# Constraint: The number of each aircraft allocated must not exceed its availability\nfor i in range(AircraftNum):\n    model.addConstr(Assignment.sum(i, '*') <= availability[i], name=f\"availability_{i}\")"}, {"description": "The total passenger capacity allocated to each route must satisfy the demand for that route", "status": "coded", "formulation": "\\sum_{i=1}^{\\textup{AircraftNum}} \\textup{capabilities}_{i,j} \\cdot \\textup{Assignment}_{i,j} \\geq \\textup{demand}_{j}, \\quad j = 1, \\ldots, \\textup{RouteNum}", "related_variables": ["Assignment"], "related_parameters": ["AircraftNum", "capabilities", "demand", "RouteNum"], "code": "# Add demand satisfaction constraints for each route\nfor j in range(RouteNum):\n    model.addConstr(\n        gp.quicksum(capabilities[i, j] * Assignment[i, j] for i in range(AircraftNum)) >= demand[j],\n        name=f\"demand_route_{j}\"\n    )"}], "variables": [{"definition": "The number of aircraft of type i assigned to route j", "symbol": "Assignment", "shape": ["AircraftNum", "RouteNum"], "status": "coded", "code": "Assignment = model.addVars(AircraftNum, RouteNum, vtype=gp.GRB.INTEGER, name=\"Assignment\")"}], "objective": [{"description": "Minimize the total cost of aircraft assignment to routes", "status": "coded", "formulation": "\\min \\quad \\sum_{i=1}^{\\textup{AircraftNum}} \\sum_{j=1}^{\\textup{RouteNum}} \\textup{costs}_{i,j} \\cdot \\textup{Assignment}_{i,j}", "related_variables": ["Assignment"], "related_parameters": ["AircraftNum", "RouteNum", "costs"], "code": "# Set objective\nmodel.setObjective(gp.quicksum(costs[i, j] * Assignment[i, j] for i in range(AircraftNum) for j in range(RouteNum)), gp.GRB.MINIMIZE)"}], "solution_status": "solved", "solver_output_status": 2, "error_message": null, "obj_val": 700.0, "log_folder": "logs/log_20250713162908_complexor_aircraft_assignment/", "data_json_path": "data/complexor/aircraft_assignment/data.json", "code": "\nimport json\nimport numpy as np\nimport math\n\nimport gurobipy as gp\n\n # Define model\nmodel = gp.Model('model')\n\nwith open(\"data/complexor/aircraft_assignment/data.json\", \"r\") as f:\n    data = json.load(f)\n\n\nAircraftNum = data[\"AircraftNum\"] # scalar parameter\nRouteNum = data[\"RouteNum\"] # scalar parameter\navailability = np.array(data[\"availability\"]) # ['AircraftNum']\ndemand = np.array(data[\"demand\"]) # ['RouteNum']\ncapabilities = np.array(data[\"capabilities\"]) # ['AircraftNum', 'RouteNum']\ncosts = np.array(data[\"costs\"]) # ['AircraftNum', 'RouteNum']\nAssignment = model.addVars(AircraftNum, RouteNum, vtype=gp.GRB.INTEGER, name=\"Assignment\")\n\n# Constraint: The number of each aircraft allocated must not exceed its availability\nfor i in range(AircraftNum):\n    model.addConstr(Assignment.sum(i, '*') <= availability[i], name=f\"availability_{i}\")\n\n# Add demand satisfaction constraints for each route\nfor j in range(RouteNum):\n    model.addConstr(\n        gp.quicksum(capabilities[i, j] * Assignment[i, j] for i in range(AircraftNum)) >= demand[j],\n        name=f\"demand_route_{j}\"\n    )\n\n# Set objective\nmodel.setObjective(gp.quicksum(costs[i, j] * Assignment[i, j] for i in range(AircraftNum) for j in range(RouteNum)), gp.GRB.MINIMIZE)\n\n# Optimize model\nmodel.optimize()\n\n\n\n# Get model status\nstatus = model.status\n\nobj_val = None\n# check whether the model is infeasible, has infinite solutions, or has an optimal solution\nif status == gp.GRB.INFEASIBLE:\n    obj_val = \"infeasible\"\nelif status == gp.GRB.INF_OR_UNBD:\n    obj_val = \"infeasible or unbounded\"\nelif status == gp.GRB.UNBOUNDED:\n    obj_val = \"unbounded\"\nelif status == gp.GRB.OPTIMAL:\n    obj_val = model.objVal\n\n"}]