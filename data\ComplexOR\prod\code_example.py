def prod(a, c, u, b):
    """
    Args:
        a: a list of integers, parameter for each element in set P
        c: a list of integers, profit coefficient for each element in set P
        u: a list of integers, upper limit for each element in set P
        b: an integer, the global constraint parameter

    Returns:
        total_profit: an integer, denotes the maximum total profit after calculation
    """
    # To be implemented
    total_profit = 0
    return total_profit