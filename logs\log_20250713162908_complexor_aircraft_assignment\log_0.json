{"background": "The Aircraft Assignment Problem aims to assign aircraft to routes in order to minimize the total cost while satisfying demand constraints with available aircraft. The problem involves a set of aircraft and a set of routes. Given the costs of assigning an aircraft to a route, the objective is to minimize the total cost of the assignment. There are limited available aircraft. It is constrained that the number of each aircraft allocated does not exceed its available number. Given the demand of each route and the capabilities (the largest number of people can be carried) of an aircraft for a route, the demand constraint ensures that the total allocation for each route satisfies the demand.", "problem_type": "LP", "parameters": [{"symbol": "AircraftNum", "definition": "The number of aircraft available", "shape": [], "code": "AircraftNum = data[\"AircraftNum\"] # scalar parameter"}, {"symbol": "RouteNum", "definition": "The number of routes to be served", "shape": [], "code": "RouteNum = data[\"RouteNum\"] # scalar parameter"}, {"symbol": "availability", "definition": "The number of each aircraft available for assignment", "shape": ["AircraftNum"], "code": "availability = np.array(data[\"availability\"]) # ['AircraftNum']"}, {"symbol": "demand", "definition": "The passenger demand for each route", "shape": ["RouteNum"], "code": "demand = np.array(data[\"demand\"]) # ['RouteNum']"}, {"symbol": "capabilities", "definition": "The passenger capacity of each aircraft for each route", "shape": ["AircraftNum", "RouteNum"], "code": "capabilities = np.array(data[\"capabilities\"]) # ['AircraftNum', 'RouteNum']"}, {"symbol": "costs", "definition": "The cost of assigning each aircraft to each route", "shape": ["AircraftNum", "RouteNum"], "code": "costs = np.array(data[\"costs\"]) # ['AircraftNum', 'RouteNum']"}], "constraint": [{"description": "The number of each aircraft allocated must not exceed its availability", "status": "formulated", "formulation": "\\sum_{j=1}^{\\textup{RouteNum}} \\textup{Assignment}_{i,j} \\leq \\textup{availability}_{i} \\quad \\forall i \\in \\{1, 2, \\ldots, \\textup{AircraftNum}\\}", "related_variables": ["Assignment"], "related_parameters": ["RouteNum", "availability", "AircraftNum"]}, {"description": "The total passenger capacity allocated to each route must satisfy the demand for that route", "status": "formulated", "formulation": "\\sum_{i=1}^{\\textup{AircraftNum}} \\textup{capabilities}_{i,j} \\cdot \\textup{Assignment}_{i,j} \\geq \\textup{demand}_{j}, \\quad j = 1, \\ldots, \\textup{RouteNum}", "related_variables": ["Assignment"], "related_parameters": ["AircraftNum", "capabilities", "demand", "RouteNum"]}], "variables": [{"definition": "The number of aircraft of type i assigned to route j", "symbol": "Assignment", "shape": ["AircraftNum", "RouteNum"], "status": "formulated"}], "objective": [{"description": "Minimize the total cost of aircraft assignment to routes", "status": "formulated", "formulation": "\\min \\quad \\sum_{i=1}^{\\textup{AircraftNum}} \\sum_{j=1}^{\\textup{RouteNum}} \\textup{costs}_{i,j} \\cdot \\textup{Assignment}_{i,j}", "related_variables": ["Assignment"], "related_parameters": ["AircraftNum", "RouteNum", "costs"]}], "solution_status": null, "solver_output_status": null, "error_message": null, "obj_val": null, "log_folder": "logs/log_20250713162908_complexor_aircraft_assignment/", "data_json_path": "data/complexor/aircraft_assignment/data.json"}