#!/usr/bin/env python3
"""
Script to create missing input_targets.json files for ComplexOR dataset problems.
This prevents FileNotFoundError when running problems that don't have input_targets.json files.
"""

import os
import json
from pathlib import Path

def create_basic_input_targets(problem_dir, problem_name):
    """Create a basic input_targets.json file for a problem."""
    
    # Try to read description if available
    description_file = os.path.join(problem_dir, "description.txt")
    if os.path.exists(description_file):
        with open(description_file, 'r', encoding='utf-8') as f:
            background = f.read().strip()
    else:
        background = f"Optimization problem: {problem_name}"
    
    # Try to read sample.json to infer parameters
    sample_file = os.path.join(problem_dir, "sample.json")
    parameters = []
    
    if os.path.exists(sample_file):
        try:
            with open(sample_file, 'r', encoding='utf-8') as f:
                sample_data = json.load(f)
                
            if isinstance(sample_data, list) and len(sample_data) > 0:
                input_data = sample_data[0].get("input", {})
                
                # Create parameters based on input data structure
                for key, value in input_data.items():
                    param = {
                        "symbol": key,
                        "definition": f"Parameter {key} for the {problem_name} problem",
                        "shape": []
                    }
                    
                    # Try to infer shape from data structure
                    if isinstance(value, list):
                        if len(value) > 0 and isinstance(value[0], list):
                            # 2D array
                            param["shape"] = ["dim1", "dim2"]
                        else:
                            # 1D array
                            param["shape"] = ["dim1"]
                    
                    parameters.append(param)
                    
        except Exception as e:
            print(f"Warning: Could not parse sample.json for {problem_name}: {e}")
    
    # Create basic structure if no parameters found
    if not parameters:
        parameters = [
            {
                "symbol": "n",
                "definition": f"Problem size parameter for {problem_name}",
                "shape": []
            }
        ]
    
    # Create the input_targets.json structure
    input_targets = {
        "background": background,
        "parameters": parameters,
        "constraints": [
            f"Problem-specific constraints for {problem_name}"
        ],
        "objective": f"Optimize the objective function for {problem_name}"
    }
    
    return input_targets

def main():
    """Main function to create missing input_targets.json files."""
    complexor_dir = Path("data/ComplexOR")
    
    if not complexor_dir.exists():
        print("Error: data/ComplexOR directory not found!")
        return
    
    created_count = 0
    
    # Iterate through all problem directories
    for problem_dir in complexor_dir.iterdir():
        if problem_dir.is_dir():
            problem_name = problem_dir.name
            input_targets_file = problem_dir / "input_targets.json"
            
            # Check if input_targets.json already exists
            if not input_targets_file.exists():
                print(f"Creating input_targets.json for {problem_name}...")
                
                try:
                    # Create the input_targets.json file
                    input_targets = create_basic_input_targets(str(problem_dir), problem_name)
                    
                    with open(input_targets_file, 'w', encoding='utf-8') as f:
                        json.dump(input_targets, f, indent=4)
                    
                    created_count += 1
                    print(f"✓ Created {input_targets_file}")
                    
                except Exception as e:
                    print(f"✗ Failed to create {input_targets_file}: {e}")
            else:
                print(f"✓ {problem_name} already has input_targets.json")
    
    print(f"\nSummary: Created {created_count} input_targets.json files")

if __name__ == "__main__":
    main()
