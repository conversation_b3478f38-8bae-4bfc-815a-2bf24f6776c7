{"background": "The Aircraft Assignment Problem aims to assign aircraft to routes in order to minimize the total cost while satisfying demand constraints with available aircraft. The problem involves a set of aircraft and a set of routes. Given the costs of assigning an aircraft to a route, the objective is to minimize the total cost of the assignment. There are limited available aircraft. It is constrained that the number of each aircraft allocated does not exceed its available number. Given the demand of each route and the capabilities (the largest number of people can be carried) of an aircraft for a route, the demand constraint ensures that the total allocation for each route satisfies the demand.", "parameters": [{"symbol": "AircraftNum", "definition": "The number of aircraft available", "shape": []}, {"symbol": "RouteNum", "definition": "The number of routes to be served", "shape": []}, {"symbol": "availability", "definition": "The number of each aircraft available for assignment", "shape": ["AircraftNum"]}, {"symbol": "demand", "definition": "The passenger demand for each route", "shape": ["RouteNum"]}, {"symbol": "capabilities", "definition": "The passenger capacity of each aircraft for each route", "shape": ["AircraftNum", "RouteNum"]}, {"symbol": "costs", "definition": "The cost of assigning each aircraft to each route", "shape": ["AircraftNum", "RouteNum"]}], "constraints": ["The number of each aircraft allocated must not exceed its availability", "The total passenger capacity allocated to each route must satisfy the demand for that route"], "objective": "Minimize the total cost of aircraft assignment to routes"}