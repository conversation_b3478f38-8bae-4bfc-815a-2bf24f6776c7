{"background": "This is a cutting stock problem. Given a roll of width `RollWidth` and a set of widths `Width` to be cut. Each width `i` has a certain number of Orders `Orders_{i}`. There are `NumPatterns` patterns and each pattern `j` has a certain number of rolls of each width `i` `NumRollsWidth_{i, j}`. The problem aims to minimize the total number of raw rolls cut. It is constrained that for each width `i`, the total number of rolls cut meets the total Orders. How to decide the number of rolls cut using each pattern `j`?", "parameters": [{"symbol": "rollWidth", "definition": "Parameter rollWidth for the cutting_stock problem", "shape": []}, {"symbol": "widthsCount", "definition": "Number of widths", "shape": []}, {"symbol": "widths", "definition": "Parameter widths for the cutting_stock problem", "shape": ["widthsCount"]}, {"symbol": "ordersCount", "definition": "Number of orders", "shape": []}, {"symbol": "orders", "definition": "Parameter orders for the cutting_stock problem", "shape": ["ordersCount"]}, {"symbol": "numPatterns", "definition": "Parameter numPatterns for the cutting_stock problem", "shape": []}, {"symbol": "numRollsWidthRows", "definition": "Number of rows in numRollsWidth", "shape": []}, {"symbol": "numRollsWidthCols", "definition": "Number of columns in numRollsWidth", "shape": []}, {"symbol": "numRollsWidth", "definition": "Parameter numRollsWidth for the cutting_stock problem", "shape": ["numRollsWidthRows", "numRollsWidthCols"]}], "constraints": ["Problem-specific constraints for cutting_stock"], "objective": "Optimize the objective function for cutting_stock"}