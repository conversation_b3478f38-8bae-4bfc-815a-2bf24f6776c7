{"background": "This is a cutting stock problem. Given a roll of width `RollWidth` and a set of widths `Width` to be cut. Each width `i` has a certain number of Orders `Orders_{i}`. There are `NumPatterns` patterns and each pattern `j` has a certain number of rolls of each width `i` `NumRollsWidth_{i, j}`. The problem aims to minimize the total number of raw rolls cut. It is constrained that for each width `i`, the total number of rolls cut meets the total Orders. How to decide the number of rolls cut using each pattern `j`?", "parameters": [{"symbol": "widthsNum", "definition": "Number of widths", "shape": []}, {"symbol": "ordersNum", "definition": "Number of orders", "shape": []}, {"symbol": "num_rolls_widthDim1", "definition": "First dimension of num_rolls_width", "shape": []}, {"symbol": "num_rolls_widthDim2", "definition": "Second dimension of num_rolls_width", "shape": []}, {"symbol": "roll_width", "definition": "Parameter roll_width for the cutting_stock problem", "shape": []}, {"symbol": "widths", "definition": "Parameter widths for the cutting_stock problem", "shape": ["widthsNum"]}, {"symbol": "orders", "definition": "Parameter orders for the cutting_stock problem", "shape": ["ordersNum"]}, {"symbol": "num_patterns", "definition": "Parameter num_patterns for the cutting_stock problem", "shape": []}, {"symbol": "num_rolls_width", "definition": "Parameter num_rolls_width for the cutting_stock problem", "shape": ["num_rolls_widthDim1", "num_rolls_widthDim2"]}], "constraints": ["Problem-specific constraints for cutting_stock"], "objective": "Optimize the objective function for cutting_stock"}