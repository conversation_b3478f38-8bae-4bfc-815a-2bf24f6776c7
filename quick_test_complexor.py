#!/usr/bin/env python3
"""
ComplexOR数据集快速验证脚本
快速检查所有问题是否能正常启动（不等待完成）
"""

import os
import sys
import time
import json
import subprocess
from pathlib import Path
from datetime import datetime

def get_all_complexor_problems():
    """获取所有ComplexOR问题列表"""
    complexor_dir = Path("data/ComplexOR")
    problems = []
    
    if not complexor_dir.exists():
        print("错误: data/ComplexOR 目录不存在!")
        return []
    
    for problem_dir in complexor_dir.iterdir():
        if problem_dir.is_dir():
            input_targets_file = problem_dir / "input_targets.json"
            if input_targets_file.exists():
                problems.append(problem_dir.name)
    
    return sorted(problems)

def quick_test_problem(problem_name, model="deepseek-reasoner", timeout=30):
    """快速测试问题是否能启动"""
    print(f"测试 {problem_name}...", end=" ")
    
    cmd = [
        "python", "run.py",
        "--dataset", "ComplexOR", 
        "--problem", problem_name,
        "--model", model
    ]
    
    try:
        # 只运行很短时间，检查是否能正常启动
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待一小段时间
        time.sleep(timeout)
        
        # 检查进程状态
        if process.poll() is None:
            # 进程还在运行，说明启动成功
            process.terminate()
            process.wait(timeout=5)
            print("✅ 启动成功")
            return True
        else:
            # 进程已结束，检查是否有错误
            stdout, stderr = process.communicate()
            if process.returncode != 0:
                print(f"❌ 启动失败: {stderr.strip()}")
                return False
            else:
                print("✅ 快速完成")
                return True
                
    except Exception as e:
        print(f"💥 异常: {str(e)}")
        return False

def main():
    print("ComplexOR数据集快速验证")
    print("=" * 50)
    
    problems = get_all_complexor_problems()
    print(f"发现 {len(problems)} 个问题")
    print()
    
    success_count = 0
    failed_problems = []
    
    for problem in problems:
        if quick_test_problem(problem):
            success_count += 1
        else:
            failed_problems.append(problem)
    
    print()
    print("=" * 50)
    print(f"验证完成: {success_count}/{len(problems)} 个问题可以正常启动")
    
    if failed_problems:
        print(f"失败的问题: {failed_problems}")
    else:
        print("🎉 所有问题都可以正常启动!")

if __name__ == "__main__":
    main()
