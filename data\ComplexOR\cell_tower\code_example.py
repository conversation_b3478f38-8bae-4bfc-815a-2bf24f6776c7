def cell_tower(delta, cost, population, budget):
    """
    Args:
        delta: A matrix (list of lists) where delta[i][j] is 1 if site `i` covers region `j`, otherwise 0
        cost: A list where cost[i] is the cost of building the tower at site `i`
        population: A list where population[j] is the population of region `j`
        budget: An integer, the total budget allowed for building the towers

    Returns:
        total_population_covered: An integer indicating the maximum population covered within the given budget
    """
    # To be implemented
    total_population_covered = 0
    return total_population_covered