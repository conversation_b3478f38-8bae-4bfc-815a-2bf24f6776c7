#!/usr/bin/env python3
"""
运行所有可以正常工作的ComplexOR问题
这是一个简化版本，只测试已验证可以启动的问题
"""

import subprocess
import time
from datetime import datetime

# 可以正常工作的问题列表
WORKING_PROBLEMS = [
    'aircraft_assignment',
    'aircraft_landing', 
    'cell_tower',
    'diet_problem',
    'knapsack_optimization',
    'multi',
    'netasgn',
    'netmcol',
    'nltrans',
    'prod',
    'steel4'
]

def run_problem(problem_name, timeout=300):
    """运行单个问题"""
    print(f"\n🚀 开始测试: {problem_name}")
    print(f"⏰ 超时时间: {timeout}秒")
    
    start_time = time.time()
    
    cmd = [
        "python", "run.py",
        "--dataset", "ComplexOR",
        "--problem", problem_name,
        "--model", "deepseek-reasoner"
    ]
    
    try:
        result = subprocess.run(
            cmd,
            timeout=timeout,
            capture_output=True,
            text=True
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        if result.returncode == 0:
            print(f"✅ {problem_name} 成功完成 (耗时: {duration:.1f}秒)")
            return True, duration
        else:
            print(f"❌ {problem_name} 失败 (耗时: {duration:.1f}秒)")
            print(f"错误: {result.stderr[:200]}...")
            return False, duration
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {problem_name} 超时 (>{timeout}秒)")
        return False, timeout
    except Exception as e:
        print(f"💥 {problem_name} 异常: {str(e)}")
        return False, 0

def main():
    """主函数"""
    print("🎯 ComplexOR数据集全自动测试")
    print("=" * 60)
    print(f"📊 测试问题数: {len(WORKING_PROBLEMS)}")
    print(f"🤖 使用模型: deepseek-reasoner")
    print(f"📅 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    results = []
    total_start_time = time.time()
    
    for i, problem in enumerate(WORKING_PROBLEMS, 1):
        print(f"\n📈 进度: {i}/{len(WORKING_PROBLEMS)}")
        success, duration = run_problem(problem, timeout=300)
        results.append({
            'problem': problem,
            'success': success,
            'duration': duration
        })
    
    total_end_time = time.time()
    total_duration = total_end_time - total_start_time
    
    # 统计结果
    success_count = sum(1 for r in results if r['success'])
    failed_count = len(results) - success_count
    avg_duration = sum(r['duration'] for r in results) / len(results)
    
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)
    print(f"✅ 成功: {success_count}/{len(WORKING_PROBLEMS)} ({success_count/len(WORKING_PROBLEMS)*100:.1f}%)")
    print(f"❌ 失败: {failed_count}")
    print(f"⏱️  总耗时: {total_duration:.1f}秒 ({total_duration/60:.1f}分钟)")
    print(f"📊 平均耗时: {avg_duration:.1f}秒/问题")
    
    if failed_count > 0:
        print(f"\n❌ 失败的问题:")
        for r in results:
            if not r['success']:
                print(f"   - {r['problem']}")
    
    print(f"\n🎉 测试完成! 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 保存简单的结果文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    result_file = f"working_problems_results_{timestamp}.txt"
    
    with open(result_file, 'w', encoding='utf-8') as f:
        f.write(f"ComplexOR Working Problems Test Results\n")
        f.write(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Model: deepseek-reasoner\n")
        f.write(f"Success: {success_count}/{len(WORKING_PROBLEMS)}\n\n")
        
        for r in results:
            status = "✅" if r['success'] else "❌"
            f.write(f"{status} {r['problem']}: {r['duration']:.1f}s\n")
    
    print(f"📄 结果已保存到: {result_file}")

if __name__ == "__main__":
    main()
