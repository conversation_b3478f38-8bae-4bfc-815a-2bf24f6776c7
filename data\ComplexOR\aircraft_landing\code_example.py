def aircraft_landing(EarliestLanding, LatestLanding, TargetLanding, PenaltyAfterTarget, PenaltyBeforeTarget, SeparationTime):
    """
    Args:
        EarliestLanding: list of integers, earliest landing times for each aircraft.
        LatestLanding: list of integers, latest landing times for each aircraft.
        TargetLanding: list of integers, target landing times for each aircraft.
        PenaltyAfterTarget: list of integers, penalties for landing after target times for each aircraft.
        PenaltyBeforeTarget: list of integers, penalties for landing before target times for each aircraft.
        SeparationTime: 2D list of integers, separation times between each pair of aircraft.

    Returns:
        min_total_penalty: an integer, denotes the minimized total penalty after calculation.
    """
    # To be implemented
    min_total_penalty = 0
    return min_total_penalty