[{"agent_name": "Formulator", "task": "Define the mathematical formulation for the optimization problem based on the provided problem description, objective function, and parameters. Specify variables, constraints, and ensure alignment with the problem requirements.", "result": "Formulation Done! Now we can write the code."}, {"agent_name": "Programmer", "task": "Write the initial code for the optimization problem based on the mathematical formulation provided by the Formulator. Ensure the code correctly implements the variables, constraints, and objective function, and uses appropriate optimization libraries.", "result": "Coding Done! Now we can evaluate the code!"}, {"agent_name": "Evaluator", "task": "Run the optimization code created by the Programmer, identify any bugs or errors, and evaluate the performance and correctness of the solution. Report any issues found.", "result": "Evaluation Done! The problem is solved."}]