def dietu_problem(cost, f_min, f_max, n_min, n_max, amt):
    """
    Args:
        cost: list of costs for each food item, length is the number of foods
        f_min: list of minimum amounts to buy for each food item, length is the number of foods
        f_max: list of maximum amounts to buy for each food item, length is the number of foods
        n_min: list of minimum nutrient requirements, length is the number of nutrients
        n_max: list of maximum nutrient requirements, length is the number of nutrients
        amt: 2D list where amt[i][j] is the amount of nutrient i in food j, dimensions are nutrients x foods

    Returns:
        total_cost: the minimal total cost to meet the diet requirements
    """
    total_cost = 0
    return total_cost
