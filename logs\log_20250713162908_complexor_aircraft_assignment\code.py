
import json
import numpy as np
import math

import gurobipy as gp

 # Define model
model = gp.Model('model')

with open("data/complexor/aircraft_assignment/data.json", "r") as f:
    data = json.load(f)


AircraftNum = data["AircraftNum"] # scalar parameter
RouteNum = data["RouteNum"] # scalar parameter
availability = np.array(data["availability"]) # ['AircraftNum']
demand = np.array(data["demand"]) # ['RouteNum']
capabilities = np.array(data["capabilities"]) # ['AircraftNum', 'RouteNum']
costs = np.array(data["costs"]) # ['AircraftNum', 'RouteNum']
Assignment = model.addVars(AircraftNum, RouteNum, vtype=gp.GRB.INTEGER, name="Assignment")

# Constraint: The number of each aircraft allocated must not exceed its availability
for i in range(AircraftNum):
    model.addConstr(Assignment.sum(i, '*') <= availability[i], name=f"availability_{i}")

# Add demand satisfaction constraints for each route
for j in range(RouteNum):
    model.addConstr(
        gp.quicksum(capabilities[i, j] * Assignment[i, j] for i in range(AircraftNum)) >= demand[j],
        name=f"demand_route_{j}"
    )

# Set objective
model.setObjective(gp.quicksum(costs[i, j] * Assignment[i, j] for i in range(AircraftNum) for j in range(RouteNum)), gp.GRB.MINIMIZE)

# Optimize model
model.optimize()



# Get model status
status = model.status

obj_val = None
# check whether the model is infeasible, has infinite solutions, or has an optimal solution
if status == gp.GRB.INFEASIBLE:
    obj_val = "infeasible"
elif status == gp.GRB.INF_OR_UNBD:
    obj_val = "infeasible or unbounded"
elif status == gp.GRB.UNBOUNDED:
    obj_val = "unbounded"
elif status == gp.GRB.OPTIMAL:
    obj_val = model.objVal

