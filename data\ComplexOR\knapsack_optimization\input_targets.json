{"background": "The Knapsack Problem is a classic optimization problem in operations research and computer science. The problem is to determine the most valuable combination of items to include in a knapsack, given a set of items with different values and weights, and a maximum weight capacity of the knapsack.", "parameters": [{"symbol": "itemValues", "definition": "Value of each item", "shape": ["Items"]}, {"symbol": "itemWeights", "definition": "Weight of each item", "shape": ["Items"]}, {"symbol": "maxWeightKnapsack", "definition": "Maximum weight capacity of the knapsack", "shape": []}], "constraints": ["The total weight of selected items must not exceed the knapsack capacity"], "objective": "Maximize the total value of the items in the knapsack"}