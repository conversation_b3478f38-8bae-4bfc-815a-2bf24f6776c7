{"background": "The Knapsack Problem is a classic optimization problem in operations research and computer science. The problem is to determine the most valuable combination of items to include in a knapsack, given a set of items with different values and weights, and a maximum weight capacity of the knapsack. The goal is to maximize the total value of the items in the knapsack without exceeding its weight capacity.", "parameters": [{"symbol": "Items", "definition": "The number of items available for selection", "shape": []}, {"symbol": "itemValues", "definition": "The value of each item", "shape": ["Items"]}, {"symbol": "itemWeights", "definition": "The weight of each item", "shape": ["Items"]}, {"symbol": "maxWeightKnapsack", "definition": "The maximum weight capacity of the knapsack", "shape": []}], "constraints": ["The total weight of selected items must not exceed the maximum weight capacity of the knapsack", "Each item can be selected at most once"], "objective": "Maximize the total value of items selected for the knapsack"}