{"background": "The Car Selection Problem is a mixed integer programming model that aims to assign participants to cars in a way that maximizes the total number of assignments. The problem involves a set of participants and a set of cars, where each participant is interested in a subset of cars. The objective is to find the optimal assignment of participants to cars that satisfies certain constraints.", "parameters": [{"symbol": "participantsCount", "definition": "Number of participants", "shape": []}, {"symbol": "participants", "definition": "Parameter participants for the car_selection problem", "shape": ["participantsCount"]}, {"symbol": "carsCount", "definition": "Number of cars", "shape": []}, {"symbol": "cars", "definition": "Parameter cars for the car_selection problem", "shape": ["carsCount"]}, {"symbol": "possibleAssignmentsRows", "definition": "Number of rows in possibleAssignments", "shape": []}, {"symbol": "possibleAssignmentsCols", "definition": "Number of columns in possibleAssignments", "shape": []}, {"symbol": "possibleAssignments", "definition": "Parameter possibleAssignments for the car_selection problem", "shape": ["possibleAssignmentsRows", "possibleAssignmentsCols"]}], "constraints": ["Problem-specific constraints for car_selection"], "objective": "Optimize the objective function for car_selection"}