#!/usr/bin/env python3
"""
为ComplexOR数据集创建缺失的data.json文件
基于sample.json文件自动生成data.json
"""

import os
import json
from pathlib import Path

def extract_data_from_sample(sample_file):
    """从sample.json提取数据并转换为data.json格式"""
    try:
        with open(sample_file, 'r', encoding='utf-8') as f:
            sample_data = json.load(f)
        
        if isinstance(sample_data, list) and len(sample_data) > 0:
            input_data = sample_data[0].get("input", {})
            
            # 转换数据格式
            data = {}
            
            # 添加维度信息
            for key, value in input_data.items():
                if isinstance(value, list):
                    if len(value) > 0 and isinstance(value[0], list):
                        # 2D数组 - 添加两个维度
                        dim1_name = f"{key.title()}Dim1"
                        dim2_name = f"{key.title()}Dim2"
                        data[dim1_name] = len(value)
                        data[dim2_name] = len(value[0]) if value else 0
                    else:
                        # 1D数组 - 添加一个维度
                        dim_name = f"{key.title()}Num"
                        data[dim_name] = len(value)
                
                # 添加原始数据
                data[key] = value
            
            return data
        
    except Exception as e:
        print(f"解析sample.json失败: {e}")
        return None
    
    return None

def create_basic_data_json(problem_name):
    """创建基本的data.json结构"""
    return {
        "n": 1,
        "description": f"Basic data for {problem_name} problem"
    }

def create_data_json_for_problem(problem_dir):
    """为单个问题创建data.json文件"""
    problem_name = problem_dir.name
    data_file = problem_dir / "data.json"
    sample_file = problem_dir / "sample.json"
    
    # 如果data.json已存在，跳过
    if data_file.exists():
        return True, "已存在"
    
    # 尝试从sample.json生成
    if sample_file.exists():
        data = extract_data_from_sample(sample_file)
        if data:
            try:
                with open(data_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=4)
                return True, "从sample.json生成"
            except Exception as e:
                print(f"写入data.json失败: {e}")
                return False, f"写入失败: {e}"
    
    # 创建基本的data.json
    try:
        data = create_basic_data_json(problem_name)
        with open(data_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=4)
        return True, "创建基本结构"
    except Exception as e:
        return False, f"创建失败: {e}"

def main():
    """主函数"""
    complexor_dir = Path("data/ComplexOR")
    
    if not complexor_dir.exists():
        print("错误: data/ComplexOR 目录不存在!")
        return
    
    print("为ComplexOR数据集创建缺失的data.json文件")
    print("=" * 60)
    
    created_count = 0
    failed_count = 0
    
    # 遍历所有问题目录
    for problem_dir in sorted(complexor_dir.iterdir()):
        if problem_dir.is_dir():
            problem_name = problem_dir.name
            success, message = create_data_json_for_problem(problem_dir)
            
            if success:
                if message == "已存在":
                    print(f"✓ {problem_name}: {message}")
                else:
                    print(f"✅ {problem_name}: {message}")
                    created_count += 1
            else:
                print(f"❌ {problem_name}: {message}")
                failed_count += 1
    
    print("=" * 60)
    print(f"总结: 创建了 {created_count} 个data.json文件")
    if failed_count > 0:
        print(f"失败: {failed_count} 个")
    else:
        print("🎉 所有问题都有data.json文件了!")

if __name__ == "__main__":
    main()
