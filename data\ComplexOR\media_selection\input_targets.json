{"background": "The main media selection problem is a problem of allocating advertising budgets between possible advertising outlets. Given a set of media options, it aims to determine which media should be selected so that all audiences are reached with minimum campaign cost. It does not matter if an audience is covered more than once, as long as it is covered at least once. Moreover, the company does not wish to spend more money on the campaign than necessary.", "parameters": [{"symbol": "target_audiences", "definition": "Parameter target_audiences for the media_selection problem", "shape": ["dim1"]}, {"symbol": "advertising_media", "definition": "Parameter advertising_media for the media_selection problem", "shape": ["dim1"]}, {"symbol": "incidence_matrix", "definition": "Parameter incidence_matrix for the media_selection problem", "shape": ["dim1", "dim2"]}, {"symbol": "media_costs", "definition": "Parameter media_costs for the media_selection problem", "shape": ["dim1"]}], "constraints": ["Problem-specific constraints for media_selection"], "objective": "Optimize the objective function for media_selection"}