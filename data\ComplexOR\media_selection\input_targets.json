{"background": "The main media selection problem is a problem of allocating advertising budgets between possible advertising outlets. Given a set of media options, it aims to determine which media should be selected so that all audiences are reached with minimum campaign cost. It does not matter if an audience is covered more than once, as long as it is covered at least once. Moreover, the company does not wish to spend more money on the campaign than necessary.", "parameters": [{"symbol": "targetAudiencesCount", "definition": "Number of targetAudiences", "shape": []}, {"symbol": "targetAudiences", "definition": "Parameter targetAudiences for the media_selection problem", "shape": ["targetAudiencesCount"]}, {"symbol": "advertisingMediaCount", "definition": "Number of advertisingMedia", "shape": []}, {"symbol": "advertisingMedia", "definition": "Parameter advertisingMedia for the media_selection problem", "shape": ["advertisingMediaCount"]}, {"symbol": "incidenceMatrixRows", "definition": "Number of rows in incidenceMatrix", "shape": []}, {"symbol": "incidenceMatrixCols", "definition": "Number of columns in incidenceMatrix", "shape": []}, {"symbol": "incidenceMatrix", "definition": "Parameter incidenceMatrix for the media_selection problem", "shape": ["incidenceMatrixRows", "incidenceMatrixCols"]}, {"symbol": "mediaCostsCount", "definition": "Number of mediaCosts", "shape": []}, {"symbol": "mediaCosts", "definition": "Parameter mediaCosts for the media_selection problem", "shape": ["mediaCostsCount"]}], "constraints": ["Problem-specific constraints for media_selection"], "objective": "Optimize the objective function for media_selection"}