{"background": "The main media selection problem is a problem of allocating advertising budgets between possible advertising outlets. Given a set of media options, it aims to determine which media should be selected so that all audiences are reached with minimum campaign cost. It does not matter if an audience is covered more than once, as long as it is covered at least once. Moreover, the company does not wish to spend more money on the campaign than necessary.", "parameters": [{"symbol": "target_audiencesNum", "definition": "Number of target_audiences", "shape": []}, {"symbol": "advertising_mediaNum", "definition": "Number of advertising_media", "shape": []}, {"symbol": "incidence_matrixDim1", "definition": "First dimension of incidence_matrix", "shape": []}, {"symbol": "incidence_matrixDim2", "definition": "Second dimension of incidence_matrix", "shape": []}, {"symbol": "media_costsNum", "definition": "Number of media_costs", "shape": []}, {"symbol": "target_audiences", "definition": "Parameter target_audiences for the media_selection problem", "shape": ["target_audiencesNum"]}, {"symbol": "advertising_media", "definition": "Parameter advertising_media for the media_selection problem", "shape": ["advertising_mediaNum"]}, {"symbol": "incidence_matrix", "definition": "Parameter incidence_matrix for the media_selection problem", "shape": ["incidence_matrixDim1", "incidence_matrixDim2"]}, {"symbol": "media_costs", "definition": "Parameter media_costs for the media_selection problem", "shape": ["media_costsNum"]}], "constraints": ["Problem-specific constraints for media_selection"], "objective": "Optimize the objective function for media_selection"}