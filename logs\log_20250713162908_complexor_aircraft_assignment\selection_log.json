[{"agent_name": "Formulator", "task": "Define variables, constraints, and objective function for the given optimization problem based on the provided problem description and parameters.", "result": "Formulation Done! Now we can write the code."}, {"agent_name": "Programmer", "task": "Write optimization code based on the mathematical formulation provided by the Formulator, ensuring it aligns with the problem description, objective function, and parameters.", "result": "Coding Done! Now we can evaluate the code!"}, {"agent_name": "Evaluator", "task": "Run the optimization code, check for any bugs or errors, and evaluate the performance and correctness of the solution based on the problem description.", "result": "Evaluation Done! The problem is solved."}]