def multi(supply, demand, limit, cost):
    """
    Args:
        supply: a 2D list, supply[i][p] indicates the amount of product p available at origin i
        demand: a 2D list, demand[p][j] indicates the amount of product p required at destination j
        limit: a 2D list, limit[i][j] indicates the maximum total amount of all products that can be shipped from origin i to destination j
        cost: a 3D list, cost[i][j][p] indicates the shipment cost per unit of product p from origin i to destination j

    Returns:
        total_cost: a float, the minimized total cost of shipping all products
    """
    # To be implemented
    total_cost = 0.0
    return total_cost