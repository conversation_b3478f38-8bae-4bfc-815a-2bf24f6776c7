#!/usr/bin/env python3
"""
修复所有ComplexOR问题，确保全部18个问题都能正常启动
"""

import os
import json
from pathlib import Path

def to_camel_case(snake_str):
    """将snake_case转换为camelCase"""
    components = snake_str.split('_')
    return components[0] + ''.join(word.capitalize() for word in components[1:])

def fix_parameter_names(data):
    """修复参数名称，确保符合camelCase规范"""
    fixed_data = {}
    
    for key, value in data.items():
        # 转换为camelCase
        if '_' in key:
            new_key = to_camel_case(key)
        else:
            new_key = key
        
        fixed_data[new_key] = value
    
    return fixed_data

def create_proper_input_targets(problem_dir, sample_data):
    """基于sample数据创建正确的input_targets.json"""
    problem_name = problem_dir.name
    
    # 读取描述
    description_file = problem_dir / "description.txt"
    if description_file.exists():
        with open(description_file, 'r', encoding='utf-8') as f:
            background = f.read().strip()
    else:
        background = f"Optimization problem: {problem_name}"
    
    input_data = sample_data[0].get("input", {})
    parameters = []
    
    # 分析数据结构并创建参数
    for key, value in input_data.items():
        # 转换为camelCase
        camel_key = to_camel_case(key) if '_' in key else key
        
        if isinstance(value, list):
            if len(value) > 0 and isinstance(value[0], list):
                # 2D数组 - 添加维度参数
                dim1_name = f"{camel_key}Rows"
                dim2_name = f"{camel_key}Cols"
                
                parameters.append({
                    "symbol": dim1_name,
                    "definition": f"Number of rows in {camel_key}",
                    "shape": []
                })
                parameters.append({
                    "symbol": dim2_name,
                    "definition": f"Number of columns in {camel_key}",
                    "shape": []
                })
                
                # 添加数据参数
                parameters.append({
                    "symbol": camel_key,
                    "definition": f"Parameter {camel_key} for the {problem_name} problem",
                    "shape": [dim1_name, dim2_name]
                })
            else:
                # 1D数组 - 添加维度参数
                dim_name = f"{camel_key}Count"
                
                parameters.append({
                    "symbol": dim_name,
                    "definition": f"Number of {camel_key}",
                    "shape": []
                })
                
                # 添加数据参数
                parameters.append({
                    "symbol": camel_key,
                    "definition": f"Parameter {camel_key} for the {problem_name} problem",
                    "shape": [dim_name]
                })
        else:
            # 标量参数
            parameters.append({
                "symbol": camel_key,
                "definition": f"Parameter {camel_key} for the {problem_name} problem",
                "shape": []
            })
    
    return {
        "background": background,
        "parameters": parameters,
        "constraints": [f"Problem-specific constraints for {problem_name}"],
        "objective": f"Optimize the objective function for {problem_name}"
    }

def create_proper_data_json(sample_data):
    """基于sample数据创建正确的data.json"""
    input_data = sample_data[0].get("input", {})
    data = {}
    
    for key, value in input_data.items():
        # 转换为camelCase
        camel_key = to_camel_case(key) if '_' in key else key
        
        if isinstance(value, list):
            if len(value) > 0 and isinstance(value[0], list):
                # 2D数组
                data[f"{camel_key}Rows"] = len(value)
                data[f"{camel_key}Cols"] = len(value[0]) if value else 0
            else:
                # 1D数组
                data[f"{camel_key}Count"] = len(value)
        
        # 添加原始数据
        data[camel_key] = value
    
    return data

def fix_single_problem(problem_dir):
    """修复单个问题"""
    problem_name = problem_dir.name
    print(f"修复 {problem_name}...")
    
    sample_file = problem_dir / "sample.json"
    data_file = problem_dir / "data.json"
    input_targets_file = problem_dir / "input_targets.json"
    
    if not sample_file.exists():
        print(f"  ❌ 缺少sample.json")
        return False
    
    try:
        # 读取sample数据
        with open(sample_file, 'r', encoding='utf-8') as f:
            sample_data = json.load(f)
        
        if not isinstance(sample_data, list) or len(sample_data) == 0:
            print(f"  ❌ sample.json格式错误")
            return False
        
        # 创建新的data.json
        new_data = create_proper_data_json(sample_data)
        with open(data_file, 'w', encoding='utf-8') as f:
            json.dump(new_data, f, indent=4)
        
        # 创建新的input_targets.json
        new_input_targets = create_proper_input_targets(problem_dir, sample_data)
        with open(input_targets_file, 'w', encoding='utf-8') as f:
            json.dump(new_input_targets, f, indent=4)
        
        print(f"  ✅ 修复成功")
        return True
        
    except Exception as e:
        print(f"  ❌ 修复失败: {e}")
        return False

def main():
    """主函数"""
    complexor_dir = Path("data/ComplexOR")
    
    if not complexor_dir.exists():
        print("错误: data/ComplexOR 目录不存在!")
        return
    
    # 需要修复的问题列表
    failed_problems = [
        'blend_problem', 'car_selection', 'cutting_stock', 
        'dietu_problem', 'flowshop_scheduling', 'media_selection', 
        'revenue_maximization'
    ]
    
    print("修复失败的ComplexOR问题")
    print("=" * 50)
    
    success_count = 0
    
    for problem_name in failed_problems:
        problem_dir = complexor_dir / problem_name
        if problem_dir.exists():
            if fix_single_problem(problem_dir):
                success_count += 1
    
    print("=" * 50)
    print(f"修复完成: {success_count}/{len(failed_problems)} 个问题")
    
    if success_count == len(failed_problems):
        print("🎉 所有问题都修复成功!")
        print("\n现在运行验证脚本检查所有问题...")
        
        # 运行验证
        import subprocess
        try:
            result = subprocess.run(
                ["python", "quick_test_complexor.py"],
                capture_output=True,
                text=True,
                timeout=300
            )
            print("验证结果:")
            print(result.stdout)
        except Exception as e:
            print(f"验证失败: {e}")
    else:
        print(f"还有 {len(failed_problems) - success_count} 个问题需要手动处理")

if __name__ == "__main__":
    main()
