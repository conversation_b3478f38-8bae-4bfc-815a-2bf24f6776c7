{"background": "The Knapsack Problem is a classic optimization problem in operations research and computer science. The problem is to determine the most valuable combination of items to include in a knapsack, given a set of items with different values and weights, and a maximum weight capacity of the knapsack. The goal is to maximize the total value of the items in the knapsack without exceeding its weight capacity.", "problem_type": "LP", "parameters": [{"symbol": "Items", "definition": "The number of items available for selection", "shape": [], "code": "Items = data[\"Items\"] # scalar parameter"}, {"symbol": "itemValues", "definition": "The value of each item", "shape": ["Items"], "code": "itemValues = np.array(data[\"itemValues\"]) # ['Items']"}, {"symbol": "itemWeights", "definition": "The weight of each item", "shape": ["Items"], "code": "itemWeights = np.array(data[\"itemWeights\"]) # ['Items']"}, {"symbol": "maxWeightKnapsack", "definition": "The maximum weight capacity of the knapsack", "shape": [], "code": "maxWeightKnapsack = data[\"maxWeightKnapsack\"] # scalar parameter"}], "constraint": [{"description": "The total weight of selected items must not exceed the maximum weight capacity of the knapsack", "status": "formulated", "formulation": "\\sum_{i=1}^{\\textup{Items}} \\textup{itemWeights}_{i} \\times \\textup{ItemSelected}_{i} \\leq \\textup{maxWeightKnapsack}", "related_variables": ["ItemSelected"], "related_parameters": ["Items", "itemWeights", "maxWeightKnapsack"]}, {"description": "Each item can be selected at most once", "status": "formulated", "formulation": "\\textup{ItemSelected}_{i} \\leq 1 \\quad \\forall i \\in \\{1, 2, \\ldots, \\textup{Items}\\}", "related_variables": ["ItemSelected"], "related_parameters": ["Items"]}, {"description": "Total weight of selected items must not exceed knapsack capacity", "formulation": "\\sum_{i=1}^{\\textup{Items}} \\textup{itemWeights}_{i} \\cdot \\textup{ItemSelected}_{i} \\leq \\textup{maxWeightKnapsack}", "status": "formulated", "related_variables": ["ItemSelected"], "related_parameters": ["Items", "itemWeights", "maxWeightKnapsack"]}], "variables": [{"definition": "Binary variable indicating if item i is selected (1) or not (0)", "symbol": "ItemSelected", "shape": ["Items"], "status": "formulated"}], "objective": [{"description": "Maximize the total value of items selected for the knapsack", "status": "formulated", "formulation": "\\max \\quad \\sum_{i=1}^{\\textup{Items}} \\textup{itemValues}_{i} \\cdot \\textup{ItemSelected}_{i}", "related_variables": ["ItemSelected"], "related_parameters": ["Items", "itemValues"]}], "solution_status": null, "solver_output_status": null, "error_message": null, "obj_val": null, "log_folder": "logs/log_20250713160806_complexor_knapsack_optimization/", "data_json_path": "data/complexor/knapsack_optimization/data.json"}