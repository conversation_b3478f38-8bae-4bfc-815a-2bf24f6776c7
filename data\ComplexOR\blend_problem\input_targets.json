{"background": "The problem aims to determine the optimal amounts of alloys to purchase in order to achieve a desired blend of required elements at the minimum cost. We are given a set of alloys available on the market and a set of required elements for the blend, the percentage composition data of each required element in each alloy, the desired blend percentage of each required element, the price of each alloy. The decision is the amount of each alloy to be purchased, which is continuous. The objective is to minimize the total cost of the alloy purchased. There are two constraints. The first set of constraints ensures that the desired blend percentage of each required element is met. The second constraint ensures that the total amount of alloys purchased is equal to 1.", "parameters": [{"symbol": "alloysOnMarketCount", "definition": "Number of alloysOnMarket", "shape": []}, {"symbol": "alloysOnMarket", "definition": "Parameter alloysOnMarket for the blend_problem problem", "shape": ["alloysOnMarketCount"]}, {"symbol": "requiredElementsCount", "definition": "Number of requiredElements", "shape": []}, {"symbol": "requiredElements", "definition": "Parameter requiredElements for the blend_problem problem", "shape": ["requiredElementsCount"]}, {"symbol": "compositionDataRows", "definition": "Number of rows in compositionData", "shape": []}, {"symbol": "compositionDataCols", "definition": "Number of columns in compositionData", "shape": []}, {"symbol": "compositionData", "definition": "Parameter compositionData for the blend_problem problem", "shape": ["compositionDataRows", "compositionDataCols"]}, {"symbol": "desiredBlendPercentageCount", "definition": "Number of desiredBlendPercentage", "shape": []}, {"symbol": "desiredBlendPercentage", "definition": "Parameter desiredBlendPercentage for the blend_problem problem", "shape": ["desiredBlendPercentageCount"]}, {"symbol": "alloyPriceCount", "definition": "Number of alloyPrice", "shape": []}, {"symbol": "alloyPrice", "definition": "Parameter alloyPrice for the blend_problem problem", "shape": ["alloyPriceCount"]}], "constraints": ["Problem-specific constraints for blend_problem"], "objective": "Optimize the objective function for blend_problem"}