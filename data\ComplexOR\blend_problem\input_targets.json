{"background": "The problem aims to determine the optimal amounts of alloys to purchase in order to achieve a desired blend of required elements at the minimum cost. We are given a set of alloys available on the market and a set of required elements for the blend, the percentage composition data of each required element in each alloy, the desired blend percentage of each required element, the price of each alloy. The decision is the amount of each alloy to be purchased, which is continuous. The objective is to minimize the total cost of the alloy purchased. There are two constraints. The first set of constraints ensures that the desired blend percentage of each required element is met. The second constraint ensures that the total amount of alloys purchased is equal to 1.", "parameters": [{"symbol": "alloys_on_market", "definition": "Parameter alloys_on_market for the blend_problem problem", "shape": ["dim1"]}, {"symbol": "required_elements", "definition": "Parameter required_elements for the blend_problem problem", "shape": ["dim1"]}, {"symbol": "composition_data", "definition": "Parameter composition_data for the blend_problem problem", "shape": ["dim1", "dim2"]}, {"symbol": "desired_blend_percentage", "definition": "Parameter desired_blend_percentage for the blend_problem problem", "shape": ["dim1"]}, {"symbol": "alloy_price", "definition": "Parameter alloy_price for the blend_problem problem", "shape": ["dim1"]}], "constraints": ["Problem-specific constraints for blend_problem"], "objective": "Optimize the objective function for blend_problem"}