def knapsack_optimization(item_values, item_weights, max_weight_knapsack):
    """
    Args:
        item_values: a list of integers, indicating the value of each item
        item_weights: a list of integers, indicating the weight of each item
        max_weight_knapsack: an integer, denotes the maximum weight capacity of the knapsack

    Returns:
        max_total_value: a float, denotes the maximum total value of the items in the knapsack
    """
    # To be implemented
    max_total_value = 0.0
    return max_total_value