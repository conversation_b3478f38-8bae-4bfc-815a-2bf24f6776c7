{"background": "Consider a transportation problem with multiple products. Given a set of cities `Cities` and a set of links `Links` between the cities. Each city `i` has a certain supply of each product `p` `Supply_{i,p}` and a certain demand for each product `p` `Demand_{i,p}`. The cost of shipping one package of product `p` from city `i` to city `j` is `ShipmentCost_{i, j, p}`. Each link `(i, j)` has a certain capacity for each product `p` `Capacity_{i,j,p}` and a joint capacity `JointCapacity_{i, j}` for all products. The problem aims to minimize the total cost of shipping products from the cities to the cities. The total number of packages to be shipped on each link `(i, j)` should not exceed its joint capacity. How to decide the number of packages of each product `p` to be shipped from each city `i` to each city `j`?", "parameters": [{"symbol": "CitiesNum", "definition": "Number of Cities", "shape": []}, {"symbol": "LinksDim1", "definition": "First dimension of Links", "shape": []}, {"symbol": "LinksDim2", "definition": "Second dimension of Links", "shape": []}, {"symbol": "ProductsNum", "definition": "Number of Products", "shape": []}, {"symbol": "SupplyDim1", "definition": "First dimension of Supply", "shape": []}, {"symbol": "SupplyDim2", "definition": "Second dimension of Supply", "shape": []}, {"symbol": "DemandDim1", "definition": "First dimension of Demand", "shape": []}, {"symbol": "DemandDim2", "definition": "Second dimension of Demand", "shape": []}, {"symbol": "ShipmentCostDim1", "definition": "First dimension of ShipmentCost", "shape": []}, {"symbol": "ShipmentCostDim2", "definition": "Second dimension of ShipmentCost", "shape": []}, {"symbol": "CapacityDim1", "definition": "First dimension of Capacity", "shape": []}, {"symbol": "CapacityDim2", "definition": "Second dimension of Capacity", "shape": []}, {"symbol": "JointCapacityDim1", "definition": "First dimension of JointCapacity", "shape": []}, {"symbol": "JointCapacityDim2", "definition": "Second dimension of JointCapacity", "shape": []}, {"symbol": "Cities", "definition": "Parameter Cities for the netmcol problem", "shape": ["CitiesNum"]}, {"symbol": "Links", "definition": "Parameter Links for the netmcol problem", "shape": ["LinksDim1", "LinksDim2"]}, {"symbol": "Products", "definition": "Parameter Products for the netmcol problem", "shape": ["ProductsNum"]}, {"symbol": "Supply", "definition": "Parameter Supply for the netmcol problem", "shape": ["SupplyDim1", "SupplyDim2"]}, {"symbol": "Demand", "definition": "Parameter Demand for the netmcol problem", "shape": ["DemandDim1", "DemandDim2"]}, {"symbol": "ShipmentCost", "definition": "Parameter ShipmentCost for the netmcol problem", "shape": ["ShipmentCostDim1", "ShipmentCostDim2"]}, {"symbol": "Capacity", "definition": "Parameter Capacity for the netmcol problem", "shape": ["CapacityDim1", "CapacityDim2"]}, {"symbol": "JointCapacity", "definition": "Parameter JointCapacity for the netmcol problem", "shape": ["JointCapacityDim1", "JointCapacityDim2"]}], "constraints": ["Problem-specific constraints for netmcol"], "objective": "Optimize the objective function for netmcol"}