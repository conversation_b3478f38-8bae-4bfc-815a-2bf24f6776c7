{"background": "Consider a transportation problem with multiple products. Given a set of cities `Cities` and a set of links `Links` between the cities. Each city `i` has a certain supply of each product `p` `Supply_{i,p}` and a certain demand for each product `p` `Demand_{i,p}`. The cost of shipping one package of product `p` from city `i` to city `j` is `ShipmentCost_{i, j, p}`. Each link `(i, j)` has a certain capacity for each product `p` `Capacity_{i,j,p}` and a joint capacity `JointCapacity_{i, j}` for all products. The problem aims to minimize the total cost of shipping products from the cities to the cities. The total number of packages to be shipped on each link `(i, j)` should not exceed its joint capacity. How to decide the number of packages of each product `p` to be shipped from each city `i` to each city `j`?", "parameters": [{"symbol": "Cities", "definition": "Parameter Cities for the netmcol problem", "shape": ["dim1"]}, {"symbol": "Links", "definition": "Parameter Links for the netmcol problem", "shape": ["dim1", "dim2"]}, {"symbol": "Products", "definition": "Parameter Products for the netmcol problem", "shape": ["dim1"]}, {"symbol": "Supply", "definition": "Parameter Supply for the netmcol problem", "shape": ["dim1", "dim2"]}, {"symbol": "Demand", "definition": "Parameter Demand for the netmcol problem", "shape": ["dim1", "dim2"]}, {"symbol": "ShipmentCost", "definition": "Parameter ShipmentCost for the netmcol problem", "shape": ["dim1", "dim2"]}, {"symbol": "Capacity", "definition": "Parameter Capacity for the netmcol problem", "shape": ["dim1", "dim2"]}, {"symbol": "JointCapacity", "definition": "Parameter JointCapacity for the netmcol problem", "shape": ["dim1", "dim2"]}], "constraints": ["Problem-specific constraints for netmcol"], "objective": "Optimize the objective function for netmcol"}