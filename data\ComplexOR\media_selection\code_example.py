def media_selection(target_audiences, advertising_media, incidence_matrix, media_costs):
    """
    Args:
        target_audiences: List of target audiences (typically a list of integers)
        advertising_media: List of advertising media (typically a list of integers)
        incidence_matrix: 2D list where incidence_matrix[t][m] indicates if audience t is covered by media m
        media_costs: List of costs associated with each advertising media

    Returns:
        min_total_cost: Minimum total cost of selected media that covers all audiences
    """
    # To be implemented
    min_total_cost = 0
    return min_total_cost