{"background": "A telecom company needs to build a set of cell towers to provide signal coverage for the inhabitants of a given city. A number of potential locations where the towers could be built have been identified. The towers have a fixed range, and due to budget constraints only a limited number of them can be built. Given these restrictions, the company wishes to provide coverage to the largest percentage of the population possible. To simplify the problem, the company has split the area it wishes to cover into a set of regions, each of which has a known population. The goal is then to choose which of the potential locations the company should build cell towers on in order to provide coverage to as many people as possible.", "problem_type": "LP", "parameters": [{"symbol": "deltaDim1", "definition": "First dimension of delta", "shape": [], "code": "deltaDim1 = data[\"deltaDim1\"] # scalar parameter"}, {"symbol": "deltaDim2", "definition": "Second dimension of delta", "shape": [], "code": "deltaDim2 = data[\"deltaDim2\"] # scalar parameter"}, {"symbol": "costNum", "definition": "Number of cost", "shape": [], "code": "costNum = data[\"costNum\"] # scalar parameter"}, {"symbol": "populationNum", "definition": "Number of population", "shape": [], "code": "populationNum = data[\"populationNum\"] # scalar parameter"}, {"symbol": "delta", "definition": "Parameter delta for the cell_tower problem", "shape": ["deltaDim1", "deltaDim2"], "code": "delta = np.array(data[\"delta\"]) # ['deltaDim1', 'deltaDim2']"}, {"symbol": "cost", "definition": "Parameter cost for the cell_tower problem", "shape": ["costNum"], "code": "cost = np.array(data[\"cost\"]) # ['costNum']"}, {"symbol": "population", "definition": "Parameter population for the cell_tower problem", "shape": ["populationNum"], "code": "population = np.array(data[\"population\"]) # ['populationNum']"}, {"symbol": "budget", "definition": "Parameter budget for the cell_tower problem", "shape": [], "code": "budget = data[\"budget\"] # scalar parameter"}], "constraint": [{"description": "Problem-specific constraints for cell_tower", "status": "formulated", "formulation": "\\sum_{i=0}^{\\textup{deltaDim1}-1} \\textup{cost}[i] \\cdot \\textup{TowerBuilt}[i] \\leq \\textup{budget}", "related_variables": ["TowerBuilt", "RegionCovered"], "related_parameters": ["deltaDim1", "cost", "budget"]}, {"description": "Coverage constraint for each region j: a region can only be marked as covered if at least one tower that covers it is built", "formulation": "\\textup{RegionCovered}[j] \\leq \\sum_{i=0}^{\\textup{deltaDim1}-1} \\delta[i][j] \\cdot \\textup{TowerBuilt}[i] \\ \\forall j \\in \\{0,1,\\dots,\\textup{deltaDim2}-1\\}", "status": "formulated", "related_variables": ["RegionCovered", "TowerBuilt"], "related_parameters": ["deltaDim1", "deltaDim2"]}, {"description": "Region j is covered only if at least one tower that covers it is built", "formulation": "\\textup{RegionCovered}_{j} \\leq \\sum_{i=1}^{\\textup{deltaDim1}} \\delta_{i,j} \\cdot \\textup{TowerBuilt}_{i} \\ \\forall j = 1, \\ldots, \\textup{deltaDim2}", "status": "formulated", "related_variables": ["RegionCovered", "TowerBuilt"], "related_parameters": ["deltaDim1", "deltaDim2"]}], "variables": [{"definition": "Binary variable indicating whether a tower is built at location i", "symbol": "TowerBuilt", "shape": ["deltaDim1"], "status": "formulated"}, {"definition": "Binary variable indicating whether region j is covered", "symbol": "RegionCovered", "shape": ["deltaDim2"], "status": "formulated"}], "objective": [{"description": "Optimize the objective function for cell_tower", "status": "formulated", "formulation": "\\max \\quad \\sum_{j=1}^{\\textup{deltaDim2}} \\textup{population}_{j} \\cdot \\textup{RegionCovered}_{j}", "related_variables": ["RegionCovered"], "related_parameters": ["deltaDim2", "population"]}], "solution_status": null, "solver_output_status": null, "error_message": null, "obj_val": null, "log_folder": "logs/log_20250713174644_complexor_cell_tower/", "data_json_path": "data/complexor/cell_tower/data.json"}