def steel4(products, stages, rate, profit, commit, market, avail):
    """
    Args:
        products: list of products
        stages: list of stages
        rate: 2D list indicating the production rate of each product at each stage
        profit: list indicating profit per ton for each product
        commit: list indicating minimum production (lower limit) for each product
        market: list indicating maximum production (upper limit) for each product
        avail: list indicating hours available per week at each stage

    Returns:
        total_profit: float, the maximum total profit
    """
    total_profit = 0
    return total_profit