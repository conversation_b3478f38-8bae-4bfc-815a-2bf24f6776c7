#!/usr/bin/env python3
"""
全自动ComplexOR数据集测试脚本
自动运行ComplexOR数据集中的所有问题，无需手动指定
"""

import os
import sys
import time
import json
import subprocess
from pathlib import Path
from datetime import datetime
import argparse

def get_all_complexor_problems():
    """获取所有ComplexOR问题列表"""
    complexor_dir = Path("data/ComplexOR")
    problems = []
    
    if not complexor_dir.exists():
        print("错误: data/ComplexOR 目录不存在!")
        return []
    
    for problem_dir in complexor_dir.iterdir():
        if problem_dir.is_dir():
            # 检查是否有必要的文件
            input_targets_file = problem_dir / "input_targets.json"
            if input_targets_file.exists():
                problems.append(problem_dir.name)
            else:
                print(f"警告: {problem_dir.name} 缺少 input_targets.json 文件，跳过")
    
    return sorted(problems)

def run_single_problem(problem_name, model="deepseek-reasoner", timeout=300):
    """运行单个问题"""
    print(f"\n{'='*60}")
    print(f"开始测试问题: {problem_name}")
    print(f"模型: {model}")
    print(f"超时时间: {timeout}秒")
    print(f"{'='*60}")
    
    start_time = time.time()
    
    # 构建命令
    cmd = [
        "python", "run.py",
        "--dataset", "ComplexOR",
        "--problem", problem_name,
        "--model", model
    ]
    
    try:
        # 运行命令
        result = subprocess.run(
            cmd,
            timeout=timeout,
            capture_output=True,
            text=True,
            encoding='utf-8'
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 检查结果
        if result.returncode == 0:
            status = "成功"
            print(f"✅ {problem_name} 完成 (耗时: {duration:.1f}秒)")
        else:
            status = "失败"
            print(f"❌ {problem_name} 失败 (耗时: {duration:.1f}秒)")
            print(f"错误输出: {result.stderr}")
        
        return {
            "problem": problem_name,
            "status": status,
            "duration": duration,
            "returncode": result.returncode,
            "stdout": result.stdout,
            "stderr": result.stderr,
            "timestamp": datetime.now().isoformat()
        }
        
    except subprocess.TimeoutExpired:
        print(f"⏰ {problem_name} 超时 (>{timeout}秒)")
        return {
            "problem": problem_name,
            "status": "超时",
            "duration": timeout,
            "returncode": -1,
            "stdout": "",
            "stderr": "Process timed out",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        print(f"💥 {problem_name} 异常: {str(e)}")
        return {
            "problem": problem_name,
            "status": "异常",
            "duration": 0,
            "returncode": -2,
            "stdout": "",
            "stderr": str(e),
            "timestamp": datetime.now().isoformat()
        }

def save_results(results, output_file):
    """保存测试结果"""
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=4, ensure_ascii=False)
    print(f"\n结果已保存到: {output_file}")

def print_summary(results):
    """打印测试总结"""
    total = len(results)
    success = len([r for r in results if r["status"] == "成功"])
    failed = len([r for r in results if r["status"] == "失败"])
    timeout = len([r for r in results if r["status"] == "超时"])
    error = len([r for r in results if r["status"] == "异常"])
    
    total_time = sum(r["duration"] for r in results)
    
    print(f"\n{'='*60}")
    print(f"测试总结")
    print(f"{'='*60}")
    print(f"总问题数: {total}")
    print(f"成功: {success} ({success/total*100:.1f}%)")
    print(f"失败: {failed} ({failed/total*100:.1f}%)")
    print(f"超时: {timeout} ({timeout/total*100:.1f}%)")
    print(f"异常: {error} ({error/total*100:.1f}%)")
    print(f"总耗时: {total_time:.1f}秒 ({total_time/60:.1f}分钟)")
    print(f"平均耗时: {total_time/total:.1f}秒/问题")
    
    if failed > 0 or timeout > 0 or error > 0:
        print(f"\n失败的问题:")
        for r in results:
            if r["status"] != "成功":
                print(f"  - {r['problem']}: {r['status']}")

def main():
    parser = argparse.ArgumentParser(description="全自动ComplexOR数据集测试")
    parser.add_argument(
        "--model", 
        type=str, 
        default="deepseek-reasoner",
        help="使用的模型 (默认: deepseek-reasoner)"
    )
    parser.add_argument(
        "--timeout", 
        type=int, 
        default=300,
        help="每个问题的超时时间(秒) (默认: 300)"
    )
    parser.add_argument(
        "--output", 
        type=str, 
        default=None,
        help="结果输出文件 (默认: 自动生成)"
    )
    parser.add_argument(
        "--problems", 
        type=str, 
        nargs="*",
        help="指定要测试的问题列表 (默认: 全部)"
    )
    parser.add_argument(
        "--skip", 
        type=str, 
        nargs="*",
        default=[],
        help="跳过的问题列表"
    )
    
    args = parser.parse_args()
    
    # 获取问题列表
    if args.problems:
        problems = args.problems
        print(f"指定测试问题: {problems}")
    else:
        problems = get_all_complexor_problems()
        print(f"发现 {len(problems)} 个ComplexOR问题")
    
    # 过滤跳过的问题
    if args.skip:
        problems = [p for p in problems if p not in args.skip]
        print(f"跳过问题: {args.skip}")
        print(f"实际测试问题数: {len(problems)}")
    
    if not problems:
        print("没有找到可测试的问题!")
        return
    
    # 生成输出文件名
    if args.output is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        args.output = f"complexor_test_results_{timestamp}.json"
    
    print(f"\n开始全自动测试...")
    print(f"模型: {args.model}")
    print(f"超时: {args.timeout}秒")
    print(f"输出文件: {args.output}")
    print(f"问题列表: {problems}")
    
    # 运行所有测试
    results = []
    start_time = time.time()
    
    for i, problem in enumerate(problems, 1):
        print(f"\n进度: {i}/{len(problems)}")
        result = run_single_problem(problem, args.model, args.timeout)
        results.append(result)
        
        # 实时保存结果
        save_results(results, args.output)
    
    end_time = time.time()
    total_duration = end_time - start_time
    
    # 添加总体信息
    summary = {
        "total_problems": len(problems),
        "total_duration": total_duration,
        "model": args.model,
        "timeout": args.timeout,
        "timestamp": datetime.now().isoformat(),
        "results": results
    }
    
    # 保存最终结果
    save_results(summary, args.output)
    
    # 打印总结
    print_summary(results)
    print(f"\n🎉 全部测试完成! 总耗时: {total_duration:.1f}秒 ({total_duration/60:.1f}分钟)")

if __name__ == "__main__":
    main()
