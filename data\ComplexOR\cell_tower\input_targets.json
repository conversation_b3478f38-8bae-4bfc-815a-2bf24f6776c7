{"background": "A telecom company needs to build a set of cell towers to provide signal coverage for the inhabitants of a given city. A number of potential locations where the towers could be built have been identified. The towers have a fixed range, and due to budget constraints only a limited number of them can be built. Given these restrictions, the company wishes to provide coverage to the largest percentage of the population possible. To simplify the problem, the company has split the area it wishes to cover into a set of regions, each of which has a known population. The goal is then to choose which of the potential locations the company should build cell towers on in order to provide coverage to as many people as possible.", "parameters": [{"symbol": "delta", "definition": "Parameter delta for the cell_tower problem", "shape": ["dim1", "dim2"]}, {"symbol": "cost", "definition": "Parameter cost for the cell_tower problem", "shape": ["dim1"]}, {"symbol": "population", "definition": "Parameter population for the cell_tower problem", "shape": ["dim1"]}, {"symbol": "budget", "definition": "Parameter budget for the cell_tower problem", "shape": []}], "constraints": ["Problem-specific constraints for cell_tower"], "objective": "Optimize the objective function for cell_tower"}