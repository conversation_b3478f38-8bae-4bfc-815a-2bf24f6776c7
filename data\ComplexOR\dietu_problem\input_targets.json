{"background": "Consider a diet problem. Given a set of foods `Foods` and a set of nutrients `Nutrients` which is the union of nutrients with minimum requirements `MinRequirements` and nutrients with maximum requirements `MaxRequirements`. Each food `j` has a cost `Cost_{j}` and the amount of each nutrient `i` it contains is `NutrientAmount_{i, j}`. The problem aims to minimize the total cost of buying foods. It is constrained that the total amount of each nutrient `i` with minimum requirements in the foods bought is at least `MinRequirement_{i}` and the total amount of each nutrient `i` with maximum requirements in the foods bought is at most `MaxRequirement_{i}`. How to decide the amount of each food `j` to buy?", "parameters": [{"symbol": "costNum", "definition": "Number of cost", "shape": []}, {"symbol": "f_minNum", "definition": "Number of f_min", "shape": []}, {"symbol": "f_maxNum", "definition": "Number of f_max", "shape": []}, {"symbol": "n_minNum", "definition": "Number of n_min", "shape": []}, {"symbol": "n_maxNum", "definition": "Number of n_max", "shape": []}, {"symbol": "amtDim1", "definition": "First dimension of amt", "shape": []}, {"symbol": "amtDim2", "definition": "Second dimension of amt", "shape": []}, {"symbol": "cost", "definition": "Parameter cost for the dietu_problem problem", "shape": ["costNum"]}, {"symbol": "f_min", "definition": "Parameter f_min for the dietu_problem problem", "shape": ["f_minNum"]}, {"symbol": "f_max", "definition": "Parameter f_max for the dietu_problem problem", "shape": ["f_maxNum"]}, {"symbol": "n_min", "definition": "Parameter n_min for the dietu_problem problem", "shape": ["n_minNum"]}, {"symbol": "n_max", "definition": "Parameter n_max for the dietu_problem problem", "shape": ["n_maxNum"]}, {"symbol": "amt", "definition": "Parameter amt for the dietu_problem problem", "shape": ["amtDim1", "amtDim2"]}], "constraints": ["Problem-specific constraints for dietu_problem"], "objective": "Optimize the objective function for dietu_problem"}