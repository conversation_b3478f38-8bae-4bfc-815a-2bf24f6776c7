{"background": "Consider a diet problem. Given a set of foods `Foods` and a set of nutrients `Nutrients` which is the union of nutrients with minimum requirements `MinRequirements` and nutrients with maximum requirements `MaxRequirements`. Each food `j` has a cost `Cost_{j}` and the amount of each nutrient `i` it contains is `NutrientAmount_{i, j}`. The problem aims to minimize the total cost of buying foods. It is constrained that the total amount of each nutrient `i` with minimum requirements in the foods bought is at least `MinRequirement_{i}` and the total amount of each nutrient `i` with maximum requirements in the foods bought is at most `MaxRequirement_{i}`. How to decide the amount of each food `j` to buy?", "parameters": [{"symbol": "costCount", "definition": "Number of cost", "shape": []}, {"symbol": "cost", "definition": "Parameter cost for the dietu_problem problem", "shape": ["costCount"]}, {"symbol": "fMinCount", "definition": "Number of fMin", "shape": []}, {"symbol": "fMin", "definition": "Parameter fMin for the dietu_problem problem", "shape": ["fMinCount"]}, {"symbol": "fMaxCount", "definition": "Number of fMax", "shape": []}, {"symbol": "fMax", "definition": "Parameter fMax for the dietu_problem problem", "shape": ["fMaxCount"]}, {"symbol": "nMinCount", "definition": "Number of nMin", "shape": []}, {"symbol": "nMin", "definition": "Parameter nMin for the dietu_problem problem", "shape": ["nMinCount"]}, {"symbol": "nMaxCount", "definition": "Number of nMax", "shape": []}, {"symbol": "nMax", "definition": "Parameter nMax for the dietu_problem problem", "shape": ["nMaxCount"]}, {"symbol": "amtRows", "definition": "Number of rows in amt", "shape": []}, {"symbol": "amtCols", "definition": "Number of columns in amt", "shape": []}, {"symbol": "amt", "definition": "Parameter amt for the dietu_problem problem", "shape": ["amtRows", "amtCols"]}], "constraints": ["Problem-specific constraints for dietu_problem"], "objective": "Optimize the objective function for dietu_problem"}