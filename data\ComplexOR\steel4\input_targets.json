{"background": "Consider a production problem. Given a set of products `Products` and a set of stages `Stages`. Each product `p` has a certain production rate `Rate_{p, s}` in each stage `s` and a certain profit `Profit_{p}` per ton. Each stage `s` has a certain number of hours `Available_{s}` available per week. There are also lower and upper limits on the tons of each product sold in a week, `Commit_{p}` and `Market_{p}` respectively. The problem aims to maximize the total profit from all products. It is constrained that the total number of hours used by all products in each stage `s` may not exceed the hours available. How to decide the number of tons to be produced for each product `p`?", "parameters": [{"symbol": "productsNum", "definition": "Number of products", "shape": []}, {"symbol": "stagesNum", "definition": "Number of stages", "shape": []}, {"symbol": "rateDim1", "definition": "First dimension of rate", "shape": []}, {"symbol": "rateDim2", "definition": "Second dimension of rate", "shape": []}, {"symbol": "profitNum", "definition": "Number of profit", "shape": []}, {"symbol": "commitNum", "definition": "Number of commit", "shape": []}, {"symbol": "marketNum", "definition": "Number of market", "shape": []}, {"symbol": "availNum", "definition": "Number of avail", "shape": []}, {"symbol": "products", "definition": "Parameter products for the steel4 problem", "shape": ["productsNum"]}, {"symbol": "stages", "definition": "Parameter stages for the steel4 problem", "shape": ["stagesNum"]}, {"symbol": "rate", "definition": "Parameter rate for the steel4 problem", "shape": ["rateDim1", "rateDim2"]}, {"symbol": "profit", "definition": "Parameter profit for the steel4 problem", "shape": ["profitNum"]}, {"symbol": "commit", "definition": "Parameter commit for the steel4 problem", "shape": ["commitNum"]}, {"symbol": "market", "definition": "Parameter market for the steel4 problem", "shape": ["marketNum"]}, {"symbol": "avail", "definition": "Parameter avail for the steel4 problem", "shape": ["availNum"]}], "constraints": ["Problem-specific constraints for steel4"], "objective": "Optimize the objective function for steel4"}