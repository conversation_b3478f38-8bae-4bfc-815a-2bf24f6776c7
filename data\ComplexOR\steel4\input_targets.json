{"background": "Consider a production problem. Given a set of products `Products` and a set of stages `Stages`. Each product `p` has a certain production rate `Rate_{p, s}` in each stage `s` and a certain profit `Profit_{p}` per ton. Each stage `s` has a certain number of hours `Available_{s}` available per week. There are also lower and upper limits on the tons of each product sold in a week, `Commit_{p}` and `Market_{p}` respectively. The problem aims to maximize the total profit from all products. It is constrained that the total number of hours used by all products in each stage `s` may not exceed the hours available. How to decide the number of tons to be produced for each product `p`?", "parameters": [{"symbol": "products", "definition": "Parameter products for the steel4 problem", "shape": ["dim1"]}, {"symbol": "stages", "definition": "Parameter stages for the steel4 problem", "shape": ["dim1"]}, {"symbol": "rate", "definition": "Parameter rate for the steel4 problem", "shape": ["dim1", "dim2"]}, {"symbol": "profit", "definition": "Parameter profit for the steel4 problem", "shape": ["dim1"]}, {"symbol": "commit", "definition": "Parameter commit for the steel4 problem", "shape": ["dim1"]}, {"symbol": "market", "definition": "Parameter market for the steel4 problem", "shape": ["dim1"]}, {"symbol": "avail", "definition": "Parameter avail for the steel4 problem", "shape": ["dim1"]}], "constraints": ["Problem-specific constraints for steel4"], "objective": "Optimize the objective function for steel4"}