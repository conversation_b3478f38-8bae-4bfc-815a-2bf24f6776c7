{"background": "Consider a diet problem. Given a set of nutrients and a set of foods. Each food has a cost and a range of amount that can be bought. Each nutrient has a range of amount that should be included in the diet. The amount of nutrient in each food is specified. The problem aims to minimize the total cost of buying foods while ensuring that the total amount of each nutrient in the bought foods is within its required range.", "parameters": [{"symbol": "Foods", "definition": "The number of foods available", "shape": []}, {"symbol": "Nutrients", "definition": "The number of nutrients to consider", "shape": []}, {"symbol": "foodSet", "definition": "The set of available foods", "shape": ["Foods"]}, {"symbol": "nutrientSet", "definition": "The set of nutrients to consider", "shape": ["Nutrients"]}, {"symbol": "foodCost", "definition": "The cost of each food", "shape": ["Foods"]}, {"symbol": "minFoodAmount", "definition": "The minimum amount of each food that can be bought", "shape": ["Foods"]}, {"symbol": "maxFoodAmount", "definition": "The maximum amount of each food that can be bought", "shape": ["Foods"]}, {"symbol": "minNutrientAmount", "definition": "The minimum required amount of each nutrient", "shape": ["Nutrients"]}, {"symbol": "maxNutrientAmount", "definition": "The maximum allowed amount of each nutrient", "shape": ["Nutrients"]}, {"symbol": "nutrientAmount", "definition": "The amount of each nutrient in each food", "shape": ["Nutrients", "Foods"]}], "constraints": ["The amount of each food bought must be within its specified range", "The total amount of each nutrient from all bought foods must be within its required range"], "objective": "Minimize the total cost of buying foods"}