#!/usr/bin/env python3
"""
修复ComplexOR数据集的data.json和input_targets.json文件
确保两个文件的参数定义一致
"""

import os
import json
from pathlib import Path

def fix_problem_data(problem_dir):
    """修复单个问题的数据文件"""
    problem_name = problem_dir.name
    data_file = problem_dir / "data.json"
    input_targets_file = problem_dir / "input_targets.json"
    sample_file = problem_dir / "sample.json"
    
    print(f"修复 {problem_name}...")
    
    # 读取sample.json获取真实数据结构
    if not sample_file.exists():
        print(f"  ❌ 缺少sample.json")
        return False
    
    try:
        with open(sample_file, 'r', encoding='utf-8') as f:
            sample_data = json.load(f)
        
        if not isinstance(sample_data, list) or len(sample_data) == 0:
            print(f"  ❌ sample.json格式错误")
            return False
        
        input_data = sample_data[0].get("input", {})
        
        # 创建新的data.json
        new_data = {}
        
        # 分析每个参数的维度
        for key, value in input_data.items():
            if isinstance(value, list):
                if len(value) > 0 and isinstance(value[0], list):
                    # 2D数组
                    dim1 = len(value)
                    dim2 = len(value[0]) if value else 0
                    new_data[f"{key}Dim1"] = dim1
                    new_data[f"{key}Dim2"] = dim2
                else:
                    # 1D数组
                    new_data[f"{key}Num"] = len(value)
            
            # 添加原始数据
            new_data[key] = value
        
        # 写入data.json
        with open(data_file, 'w', encoding='utf-8') as f:
            json.dump(new_data, f, indent=4)
        
        # 创建新的input_targets.json
        parameters = []
        
        # 添加维度参数
        for key, value in input_data.items():
            if isinstance(value, list):
                if len(value) > 0 and isinstance(value[0], list):
                    # 2D数组维度
                    parameters.append({
                        "symbol": f"{key}Dim1",
                        "definition": f"First dimension of {key}",
                        "shape": []
                    })
                    parameters.append({
                        "symbol": f"{key}Dim2", 
                        "definition": f"Second dimension of {key}",
                        "shape": []
                    })
                else:
                    # 1D数组维度
                    parameters.append({
                        "symbol": f"{key}Num",
                        "definition": f"Number of {key}",
                        "shape": []
                    })
        
        # 添加数据参数
        for key, value in input_data.items():
            param = {
                "symbol": key,
                "definition": f"Parameter {key} for the {problem_name} problem",
                "shape": []
            }
            
            if isinstance(value, list):
                if len(value) > 0 and isinstance(value[0], list):
                    # 2D数组
                    param["shape"] = [f"{key}Dim1", f"{key}Dim2"]
                else:
                    # 1D数组
                    param["shape"] = [f"{key}Num"]
            
            parameters.append(param)
        
        # 读取描述
        description_file = problem_dir / "description.txt"
        if description_file.exists():
            with open(description_file, 'r', encoding='utf-8') as f:
                background = f.read().strip()
        else:
            background = f"Optimization problem: {problem_name}"
        
        new_input_targets = {
            "background": background,
            "parameters": parameters,
            "constraints": [f"Problem-specific constraints for {problem_name}"],
            "objective": f"Optimize the objective function for {problem_name}"
        }
        
        # 写入input_targets.json
        with open(input_targets_file, 'w', encoding='utf-8') as f:
            json.dump(new_input_targets, f, indent=4)
        
        print(f"  ✅ 修复成功")
        return True
        
    except Exception as e:
        print(f"  ❌ 修复失败: {e}")
        return False

def main():
    """主函数"""
    complexor_dir = Path("data/ComplexOR")
    
    if not complexor_dir.exists():
        print("错误: data/ComplexOR 目录不存在!")
        return
    
    print("修复ComplexOR数据集文件")
    print("=" * 50)
    
    success_count = 0
    total_count = 0
    
    # 只修复失败的问题
    failed_problems = [
        'aircraft_landing', 'blend_problem', 'car_selection', 'cell_tower', 
        'cutting_stock', 'dietu_problem', 'flowshop_scheduling', 'media_selection', 
        'multi', 'netasgn', 'netmcol', 'nltrans', 'prod', 'revenue_maximization', 'steel4'
    ]
    
    for problem_name in failed_problems:
        problem_dir = complexor_dir / problem_name
        if problem_dir.exists():
            total_count += 1
            if fix_problem_data(problem_dir):
                success_count += 1
    
    print("=" * 50)
    print(f"修复完成: {success_count}/{total_count} 个问题")
    
    if success_count == total_count:
        print("🎉 所有问题都修复成功!")
    else:
        print(f"还有 {total_count - success_count} 个问题需要手动处理")

if __name__ == "__main__":
    main()
