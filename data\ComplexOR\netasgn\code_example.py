def netasgn(supply, demand, cost, limit):
    """
    Args:
        supply: list, hours each person is available, length is number of people
        demand: list, hours each project requires, length is number of projects
        cost: 2D list, cost per hour of work for each person on each project, dimensions are number of people x number of projects
        limit: 2D list, maximum contributions to projects for each person on each project, dimensions are number of people x number of projects

    Returns:
        total_cost: a float, denotes the minimized total cost of assigning people to projects
    """
    # To be implemented
    total_cost = 0.0
    return total_cost