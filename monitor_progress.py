#!/usr/bin/env python3
"""
监控ComplexOR全自动测试进度
"""

import json
import time
import os
from datetime import datetime
from pathlib import Path

def find_latest_result_file():
    """找到最新的结果文件"""
    files = list(Path(".").glob("complexor_test_results_*.json"))
    if not files:
        return None
    return max(files, key=os.path.getctime)

def monitor_progress():
    """监控测试进度"""
    print("🔍 ComplexOR测试进度监控")
    print("=" * 50)
    
    last_count = 0
    
    while True:
        result_file = find_latest_result_file()
        
        if result_file and result_file.exists():
            try:
                with open(result_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                if 'results' in data:
                    results = data['results']
                    current_count = len(results)
                    
                    if current_count > last_count:
                        print(f"\n📊 进度更新 - {datetime.now().strftime('%H:%M:%S')}")
                        print(f"已完成: {current_count}/18 个问题")
                        
                        # 显示最新完成的问题
                        if current_count > 0:
                            latest = results[-1]
                            status = "✅" if latest['status'] == "成功" else "❌"
                            print(f"最新: {status} {latest['problem']} ({latest['duration']:.1f}s)")
                        
                        # 显示统计
                        success = len([r for r in results if r['status'] == "成功"])
                        failed = len([r for r in results if r['status'] != "成功"])
                        
                        if current_count > 0:
                            print(f"成功率: {success}/{current_count} ({success/current_count*100:.1f}%)")
                        
                        last_count = current_count
                        
                        # 如果全部完成
                        if current_count >= 18:
                            print(f"\n🎉 测试完成!")
                            print(f"最终结果: {success}/18 成功")
                            break
                
            except Exception as e:
                print(f"读取结果文件失败: {e}")
        
        time.sleep(30)  # 每30秒检查一次

if __name__ == "__main__":
    try:
        monitor_progress()
    except KeyboardInterrupt:
        print("\n👋 监控已停止")
