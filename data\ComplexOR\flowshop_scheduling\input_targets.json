{"background": "A set of jobs `Jobs` need to be processed on a set of machines `Machines` in series. All jobs have the same processing order through all the machines from machine 1 to machine M. Each machine can work in parallel. The workflow is the following: the first job of the sequence goes to the first machine to be processed; meanwhile, other jobs wait; when the first machine has processed the first job, the first job goes to the second machine and the second job of the sequence starts to be processed by the first machine; and so on. The time required to process job `j` on machine `m` is `ProcesTime_{j, m}`. The problem aims to minimize the total makespan. The goal is to find a sequence of jobs that minimize the makespan: the time when all jobs have been processed.", "parameters": [{"symbol": "jobsNum", "definition": "Number of jobs", "shape": []}, {"symbol": "schedulesNum", "definition": "Number of schedules", "shape": []}, {"symbol": "machinesNum", "definition": "Number of machines", "shape": []}, {"symbol": "proces_timeDim1", "definition": "First dimension of proces_time", "shape": []}, {"symbol": "proces_timeDim2", "definition": "Second dimension of proces_time", "shape": []}, {"symbol": "jobs", "definition": "Parameter jobs for the flowshop_scheduling problem", "shape": ["jobsNum"]}, {"symbol": "schedules", "definition": "Parameter schedules for the flowshop_scheduling problem", "shape": ["schedulesNum"]}, {"symbol": "machines", "definition": "Parameter machines for the flowshop_scheduling problem", "shape": ["machinesNum"]}, {"symbol": "proces_time", "definition": "Parameter proces_time for the flowshop_scheduling problem", "shape": ["proces_timeDim1", "proces_timeDim2"]}], "constraints": ["Problem-specific constraints for flowshop_scheduling"], "objective": "Optimize the objective function for flowshop_scheduling"}