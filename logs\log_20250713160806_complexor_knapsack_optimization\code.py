
import json
import numpy as np
import math

import gurobipy as gp

 # Define model
model = gp.Model('model')

with open("data/complexor/knapsack_optimization/data.json", "r") as f:
    data = json.load(f)


Items = data["Items"] # scalar parameter
itemValues = np.array(data["itemValues"]) # ['Items']
itemWeights = np.array(data["itemWeights"]) # ['Items']
maxWeightKnapsack = data["maxWeightKnapsack"] # scalar parameter
ItemSelected = model.addVars(Items, vtype=gp.GRB.BINARY, name="ItemSelected")

model.addConstr( gp.quicksum( itemWeights[i] * ItemSelected[i] for i in range(Items) ) <= maxWeightKnapsack, name="weight_capacity" )

# Constraint: Each item can be selected at most once is redundant because ItemSelected is binary (0 or 1). No constraint needed.

model.addConstr(gp.quicksum(itemWeights[i] * ItemSelected[i] for i in ItemSelected) <= maxWeightKnapsack, name="weight_capacity")

# Maximize the total value of items selected for the knapsack
model.setObjective(gp.quicksum(itemValues[i] * ItemSelected[i] for i in range(Items)), gp.GRB.MAXIMIZE)

# Optimize model
model.optimize()



# Get model status
status = model.status

obj_val = None
# check whether the model is infeasible, has infinite solutions, or has an optimal solution
if status == gp.GRB.INFEASIBLE:
    obj_val = "infeasible"
elif status == gp.GRB.INF_OR_UNBD:
    obj_val = "infeasible or unbounded"
elif status == gp.GRB.UNBOUNDED:
    obj_val = "unbounded"
elif status == gp.GRB.OPTIMAL:
    obj_val = model.objVal

