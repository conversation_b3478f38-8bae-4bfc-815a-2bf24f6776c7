{"background": "The Car Selection Problem is a mixed integer programming model that aims to assign participants to cars in a way that maximizes the total number of assignments. The problem involves a set of participants and a set of cars, where each participant is interested in a subset of cars. The objective is to find the optimal assignment of participants to cars that satisfies certain constraints.", "problem_type": "LP", "parameters": [{"symbol": "participantsCount", "definition": "Number of participants", "shape": [], "code": "participantsCount = data[\"participantsCount\"] # scalar parameter"}, {"symbol": "participants", "definition": "Parameter participants for the car_selection problem", "shape": ["participantsCount"], "code": "participants = np.array(data[\"participants\"]) # ['participantsCount']"}, {"symbol": "carsCount", "definition": "Number of cars", "shape": [], "code": "carsCount = data[\"carsCount\"] # scalar parameter"}, {"symbol": "cars", "definition": "Parameter cars for the car_selection problem", "shape": ["carsCount"], "code": "cars = np.array(data[\"cars\"]) # ['carsCount']"}, {"symbol": "possibleAssignmentsRows", "definition": "Number of rows in possibleAssignments", "shape": [], "code": "possibleAssignmentsRows = data[\"possibleAssignmentsRows\"] # scalar parameter"}, {"symbol": "possibleAssignmentsCols", "definition": "Number of columns in possibleAssignments", "shape": [], "code": "possibleAssignmentsCols = data[\"possibleAssignmentsCols\"] # scalar parameter"}, {"symbol": "possibleAssignments", "definition": "Parameter possibleAssignments for the car_selection problem", "shape": ["possibleAssignmentsRows", "possibleAssignmentsCols"], "code": "possibleAssignments = np.array(data[\"possibleAssignments\"]) # ['possibleAssignmentsRows', 'possibleAssignmentsCols']"}], "constraint": [{"description": "Problem-specific constraints for car_selection", "status": "formulated", "formulation": "\\sum_{r \\in \\mathcal{R}_i} \\textup{Assignment}_r \\leq 1 \\quad \\forall i \\in \\{1,\\dots,\\textup{participantsCount}\\}", "related_variables": ["Assignment"], "related_parameters": ["participantsCount"]}], "variables": [{"definition": "Binary variable for each possible assignment (row in possibleAssignments). Assignment_r = 1 if the assignment in row r is selected, else 0.", "symbol": "Assignment", "shape": ["possibleAssignmentsRows"], "status": "formulated"}], "objective": [{"description": "Optimize the objective function for car_selection", "status": "formulated", "formulation": "\\max \\quad \\sum_{r=1}^{\\textup{possibleAssignmentsRows}} \\textup{Assignment}_{r}", "related_variables": ["Assignment"], "related_parameters": ["possibleAssignmentsRows"]}], "solution_status": null, "solver_output_status": null, "error_message": null, "obj_val": null, "log_folder": "logs/log_20250713174144_complexor_car_selection/", "data_json_path": "data/complexor/car_selection/data.json"}