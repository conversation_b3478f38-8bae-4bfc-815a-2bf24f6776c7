{"background": "We have a set of flight legs (one-way non-stop flight) with a limited passenger capacity. According to market research, we defined a set of flight itineraries to sell as a package with a given price. For each package, we have an estimated demand. How many units of each package should we sell to maximize the revenue? We reserve the passenger seats according to the number of packages we want to sell.", "parameters": [{"symbol": "availableSeatsCount", "definition": "Number of availableSeats", "shape": []}, {"symbol": "availableSeats", "definition": "Parameter availableSeats for the revenue_maximization problem", "shape": ["availableSeatsCount"]}, {"symbol": "demandCount", "definition": "Number of demand", "shape": []}, {"symbol": "demand", "definition": "Parameter demand for the revenue_maximization problem", "shape": ["demandCount"]}, {"symbol": "revenueCount", "definition": "Number of revenue", "shape": []}, {"symbol": "revenue", "definition": "Parameter revenue for the revenue_maximization problem", "shape": ["revenueCount"]}, {"symbol": "deltaRows", "definition": "Number of rows in delta", "shape": []}, {"symbol": "deltaCols", "definition": "Number of columns in delta", "shape": []}, {"symbol": "delta", "definition": "Parameter delta for the revenue_maximization problem", "shape": ["deltaRows", "deltaCols"]}], "constraints": ["Problem-specific constraints for revenue_maximization"], "objective": "Optimize the objective function for revenue_maximization"}