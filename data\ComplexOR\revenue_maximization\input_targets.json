{"background": "We have a set of flight legs (one-way non-stop flight) with a limited passenger capacity. According to market research, we defined a set of flight itineraries to sell as a package with a given price. For each package, we have an estimated demand. How many units of each package should we sell to maximize the revenue? We reserve the passenger seats according to the number of packages we want to sell.", "parameters": [{"symbol": "available_seats", "definition": "Parameter available_seats for the revenue_maximization problem", "shape": ["dim1"]}, {"symbol": "demand", "definition": "Parameter demand for the revenue_maximization problem", "shape": ["dim1"]}, {"symbol": "revenue", "definition": "Parameter revenue for the revenue_maximization problem", "shape": ["dim1"]}, {"symbol": "delta", "definition": "Parameter delta for the revenue_maximization problem", "shape": ["dim1", "dim2"]}], "constraints": ["Problem-specific constraints for revenue_maximization"], "objective": "Optimize the objective function for revenue_maximization"}