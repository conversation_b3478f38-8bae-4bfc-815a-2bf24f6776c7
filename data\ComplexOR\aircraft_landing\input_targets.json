{"background": "The Aircraft Landing Problem (ALP) is the problem of deciding a landing time on an appropriate runway for each aircraft in a given set of aircraft such that each aircraft lands within a predetermined time window; and separation criteria between the landing of an aircraft, and the landing of all successive aircraft, are respected. We are given the earliest landing time, latest landing time, target landing time, and penalties for landing before or after the target landing time for each aircraft. There is also a separation time that represents the minimum time required between the landing of two aircraft. The objective of the problem is to minimize the total penalties of landing before or after the target time for each aircraft. The problem includes several constraints. The order constraint ensures that the aircrafts land in a specific order. The separation constraint ensures that there is enough separation time between the landing of aircraft. The lower and upper time window constraints ensure that each aircraft lands within its respective earliest and latest time windows.", "parameters": [{"symbol": "EarliestLanding", "definition": "Parameter EarliestLanding for the aircraft_landing problem", "shape": ["dim1"]}, {"symbol": "LatestLanding", "definition": "Parameter LatestLanding for the aircraft_landing problem", "shape": ["dim1"]}, {"symbol": "TargetLanding", "definition": "Parameter TargetLanding for the aircraft_landing problem", "shape": ["dim1"]}, {"symbol": "PenaltyAfter<PERSON>arget", "definition": "Parameter PenaltyAfterTarget for the aircraft_landing problem", "shape": ["dim1"]}, {"symbol": "PenaltyBeforeTarget", "definition": "Parameter PenaltyBeforeTarget for the aircraft_landing problem", "shape": ["dim1"]}, {"symbol": "SeparationTime", "definition": "Parameter SeparationTime for the aircraft_landing problem", "shape": ["dim1", "dim2"]}], "constraints": ["Problem-specific constraints for aircraft_landing"], "objective": "Optimize the objective function for aircraft_landing"}