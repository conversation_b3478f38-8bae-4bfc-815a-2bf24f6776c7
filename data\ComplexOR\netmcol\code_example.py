def netmcol(Cities, Links, Products, Supply, Demand, ShipmentCost, Capacity, JointCapacity):
    """
    Args:
        Cities: list, a list of cities
        Links: list, a list of links between the cities
        Products: list, a list of products
        Supply: list of lists, the supply of each product at each city
        Demand: list of lists, the demand of each product at each city
        ShipmentCost: list of lists of lists, the cost of shipping each product from each city to each city
        Capacity: list of lists of lists, the capacity of shipping each product from each city to each city
        JointCapacity: list of lists, the joint capacity of each link

    Returns:
        total_cost: float, the minimized total shipping cost after calculation
    """
    total_cost = 0
    return total_cost
